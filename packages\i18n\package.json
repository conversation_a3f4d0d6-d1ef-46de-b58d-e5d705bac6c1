{"name": "@extension/i18n", "version": "0.5.0", "description": "chrome extension - internationalization", "type": "module", "private": true, "sideEffects": false, "files": ["dist/**"], "main": "dist/index.mjs", "types": "index.mts", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "tsc -b prepare-build.tsconfig.json && node --env-file=../../.env dist/lib/prepare-build.js && tsc -b", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "format": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "dependencies": {"@extension/env": "workspace:*"}, "devDependencies": {"@extension/tsconfig": "workspace:*"}}