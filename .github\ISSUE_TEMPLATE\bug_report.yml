name: Bug Report
description: Create a bug report.
title: "[Bug]: "
labels: [ "bug", "triage" ]
assignees:
  - Jonghakseo
  - PatrykKuniczak

body:
  - type: textarea
    id: to-reproduce
    attributes:
      label: To Reproduce
      description: |
        Steps to reproduce the behavior.
        Use [Brie](https://go.briehq.com/github?utm_source=CEB) to capture screenshots, errors, and network activity for faster issue resolution.
      placeholder: Describe the steps to reproduce the issue.
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: Describe the expected behavior.
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem.

  - type: dropdown
    id: desktop-os
    attributes:
      label: OS
      description: Select your operating system.
      options: [ Windows, macOS, Linux ]
      default: 0
    validations:
      required: true

  - type: dropdown
    id: desktop-browser
    attributes:
      label: Browser
      description: Select the browser where the issue occurs(Multiple choice).
      multiple: true
      options: [ Chrome, Opera, Opera GX, Firefox, Safari, Microsoft Edge, Brave ]
      default: 0
    validations:
      required: true

  - type: input
    id: desktop-node-version
    attributes:
      label: Node Version
      description: Specify the Node.js version you are using.
      placeholder: 22.12.0

  - type: input
    id: desktop-other-packages
    attributes:
      label: Other Necessary Packages Version
      description: Specify versions of other necessary packages.
      placeholder: pnpm-10.10.0

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
      placeholder: Provide additional details if necessary.