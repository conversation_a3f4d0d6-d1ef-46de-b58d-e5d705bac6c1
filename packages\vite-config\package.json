{"name": "@extension/vite-config", "version": "0.5.0", "description": "chrome extension - vite base configuration", "type": "module", "private": true, "sideEffects": true, "types": "index.mts", "main": "dist/index.mjs", "module": "dist/index.mjs", "scripts": {"clean:node_modules": "pnpx rimraf node_modules", "clean:bundle": "pnpx rimraf dist", "clean": "pnpm clean:bundle && pnpm clean:node_modules", "ready": "tsc -b"}, "dependencies": {"@extension/env": "workspace:*"}, "devDependencies": {"@extension/hmr": "workspace:*", "@extension/tsconfig": "workspace:*", "@vitejs/plugin-react-swc": "^3.9.0"}}