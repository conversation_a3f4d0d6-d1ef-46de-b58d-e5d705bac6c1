{"extensionDescription": {"description": "Extension description", "message": "Chrome extension boilerplate developed with Vite, React and Typescript"}, "extensionName": {"description": "Extension name", "message": "Chrome extension boilerplate"}, "toggleTheme": {"message": "Toggle theme"}, "injectButton": {"message": "Click to inject Content Scripts"}, "greeting": {"description": "Greeting message", "message": "Hello, My name is $NAME$", "placeholders": {"name": {"content": "$1", "example": "<PERSON>"}}}, "hello": {"description": "Placeholder example", "message": "Hello $1"}, "displayErrorInfo": {"message": "Error occur"}, "displayErrorDescription": {"message": "Sorry, something went wrong while loading the page."}, "displayErrorDetailsInfo": {"message": "Error details:"}, "displayErrorUnknownErrorInfo": {"message": "Unknown error"}, "displayErrorReset": {"message": "Run Again"}}