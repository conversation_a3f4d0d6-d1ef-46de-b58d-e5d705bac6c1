{"name": "@extension/zipper", "version": "0.5.0", "description": "chrome extension - zipper", "type": "module", "private": true, "sideEffects": false, "files": ["dist/**"], "types": "index.mts", "main": "dist/index.mjs", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "zip": "node --env-file=../../.env dist/index.mjs", "lint": "eslint .", "ready": "tsc -b", "lint:fix": "pnpm lint --fix", "format": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/tsconfig": "workspace:*", "@extension/dev-utils": "workspace:*", "@extension/env": "workspace:*"}}