name: Greetings

on: [ pull_request_target, issues ]

jobs:
  greeting:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/first-interaction@v1
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          issue-message: 'Thank you for your contribution. We will check and reply to you as soon as possible.'
          pr-message: 'Thank you for your contribution. We will check and reply to you as soon as possible.'