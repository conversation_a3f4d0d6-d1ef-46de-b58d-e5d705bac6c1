{"name": "@extension/content-script", "version": "0.5.0", "description": "chrome extension - content script", "type": "module", "private": true, "sideEffects": true, "files": ["dist/**"], "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:turbo && pnpm clean:node_modules", "build": "tsx build.mts", "dev": "tsx build.mts", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "format": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "dependencies": {"@extension/shared": "workspace:*", "@extension/storage": "workspace:*", "@extension/env": "workspace:*"}, "devDependencies": {"@extension/hmr": "workspace:*", "@extension/tsconfig": "workspace:*", "@extension/vite-config": "workspace:*"}}