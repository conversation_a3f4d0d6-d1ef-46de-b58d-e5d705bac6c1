{"name": "@extension/ui", "version": "0.5.0", "description": "chrome extension - ui components", "type": "module", "private": true, "sideEffects": true, "files": ["dist/**"], "types": "index.ts", "main": "dist/index.js", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "tsc -b && tsc-alias -p tsconfig.json", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "format": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "dependencies": {"@extension/i18n": "workspace:*", "@extension/shared": "workspace:*", "@extension/storage": "workspace:*", "clsx": "^2.1.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@extension/tailwindcss-config": "workspace:*", "@extension/tsconfig": "workspace:*", "react-spinners": "^0.17.0", "tsc-alias": "^1.8.16"}}