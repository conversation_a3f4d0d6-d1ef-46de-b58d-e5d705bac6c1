name: Run E2E Tests

on: [ pull_request_target ]

permissions:
  contents: read

jobs:
  e2e:
    name: E2<PERSON> tests on ${{ matrix.browser }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chrome, firefox]
    env:
      CEB_CI: true
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: pnpm
      - run: pnpm install --frozen-lockfile --prefer-offline
      - run: pnpm ${{ matrix.browser == 'chrome' && 'e2e' || 'e2e:firefox' }}
