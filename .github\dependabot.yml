version: 2
updates:
  - package-ecosystem: "npm"
    target-branch: 'dev'
    directory: "/"
    schedule:
      interval: "daily"
    labels:
      - "dependencies"
      - "npm"
    groups:
      dev-dependencies:
        dependency-type: development
      dependencies:
        dependency-type: production
    versioning-strategy: increase

  - package-ecosystem: "github-actions"
    target-branch: 'dev'
    directory: "/"
    schedule:
      interval: "daily"
    labels:
      - "dependencies"
      - "github-actions"
