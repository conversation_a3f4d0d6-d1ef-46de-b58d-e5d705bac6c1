import '@src/Popup.css';
import { useStorage, withErrorBoundary, withSuspense } from '@extension/shared';
import { exampleThemeStorage } from '@extension/storage';
import { cn, ErrorDisplay, LoadingSpinner } from '@extension/ui';
import { useState, useEffect } from 'react';

const Popup = () => {
  const { isLight } = useStorage(exampleThemeStorage);
  const [currentUrl, setCurrentUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCozeTemplatePage, setIsCozeTemplatePage] = useState<boolean>(false);

  // 检查当前页面URL
  useEffect(() => {
    const getCurrentTab = async () => {
      try {
        const [tab] = await chrome.tabs.query({ currentWindow: true, active: true });
        if (tab.url) {
          setCurrentUrl(tab.url);
          setIsCozeTemplatePage(tab.url.includes('https://www.coze.cn/template'));
        }
      } catch (error) {
        console.error('获取当前标签页失败:', error);
      }
    };

    getCurrentTab();
  }, []);

  // 将数据转换为CSV格式
  const convertToCSV = (data: any[]) => {
    if (data.length === 0) return '';

    const headers = ['封面图', '应用类型', '标题', '作者', '简介', '费用', '复制次数'];
    const csvContent = [
      headers.join(','),
      ...data.map(item => [
        `"${item.bglmg || ''}"`,
        `"${item.appType || ''}"`,
        `"${item.title || ''}"`,
        `"${item.author || ''}"`,
        `"${item.desc || ''}"`,
        `"${item.price || ''}"`,
        `"${item.copyCount || ''}"`
      ].join(','))
    ].join('\n');

    return csvContent;
  };

  // 下载CSV文件
  const downloadCSV = (csvContent: string, filename: string) => {
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 获取COZE模板市场数据
  const getCozeTemplateData = async () => {
    if (!isCozeTemplatePage) {
      alert('请在 https://www.coze.cn/template 页面使用此功能');
      return;
    }

    setIsLoading(true);

    try {
      const [tab] = await chrome.tabs.query({ currentWindow: true, active: true });

      // 注入数据获取脚本
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id! },
        func: () => {
          // 定义选择器
          const selectors = {
            bglmg: '.semi-image-img',
            appType: '.coz-tag-brand .semi-tag-content span',
            title: '.coz-typography.coz-text.font-normal span',
            author: '.name--rm_hbL8bKLQSDcPE span',
            desc: '.coz-typography-ellipsis-multiple-line-text span',
            price: '.mt-\\[4px\\] .coz-fg-primary',
            copyCount: '.mt-\\[4px\\] .coz-fg-secondary span:first-child'
          };

          // 获取所有卡片数据
          const getAllCardsData = () => {
            const cards = document.querySelectorAll('.flex.flex-col.grow.overflow-hidden');
            return Array.from(cards).map(card => {
              return {
                bglmg: card.querySelector(selectors.bglmg)?.src || '',
                appType: card.querySelector(selectors.appType)?.textContent?.trim() || '',
                title: card.querySelector(selectors.title)?.textContent?.trim() || '',
                author: card.querySelector(selectors.author)?.textContent?.trim() || '',
                desc: card.querySelector(selectors.desc)?.textContent?.trim() || '',
                price: card.querySelector(selectors.price)?.textContent?.trim() || '',
                copyCount: card.querySelector(selectors.copyCount)?.textContent?.trim() || ''
              };
            });
          };

          return getAllCardsData();
        }
      });

      const data = results[0].result;

      if (data && data.length > 0) {
        // 转换为CSV并下载
        const csvContent = convertToCSV(data);
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `coze-template-data-${timestamp}.csv`;

        downloadCSV(csvContent, filename);

        alert(`成功获取 ${data.length} 条数据并下载为 ${filename}`);
      } else {
        alert('未找到数据，请确保页面已完全加载');
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      alert('获取数据失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="popup-container">
      <div className="header">
        <img src={chrome.runtime.getURL('popup/coze-logo.svg')} alt="COZE Logo" className="coze-logo" />
        <h1 className="title">COZE 模板市场数据获取工具</h1>
      </div>

      <div className="content">
        <div className="current-page-section">
          <p className="current-page-label">当前页面:</p>
          <div className={cn('current-page-url', isCozeTemplatePage ? 'valid' : 'invalid')}>
            {isCozeTemplatePage ? (
              <a href="https://www.coze.cn/template" target="_blank" rel="noopener noreferrer">
                https://www.coze.cn/template
              </a>
            ) : (
              <span>{currentUrl || '获取中...'}</span>
            )}
          </div>
        </div>

        <button
          className={cn(
            'fetch-btn',
            isCozeTemplatePage && !isLoading ? 'enabled' : 'disabled'
          )}
          onClick={getCozeTemplateData}
          disabled={!isCozeTemplatePage || isLoading}>
          {isLoading ? (
            <>
              <span className="loading-spinner"></span>
              获取数据中...
            </>
          ) : (
            '获取 COZE 模板市场数据'
          )}
        </button>

        {!isCozeTemplatePage && (
          <p className="error-message">
            请在 <a href="https://www.coze.cn/template" target="_blank" rel="noopener noreferrer">
              https://www.coze.cn/template
            </a> 页面使用此功能
          </p>
        )}
      </div>
    </div>
  );
};

export default withErrorBoundary(withSuspense(Popup, <LoadingSpinner />), ErrorDisplay);
