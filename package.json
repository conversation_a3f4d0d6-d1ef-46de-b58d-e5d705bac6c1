{"name": "chrome-extension-boilerplate-react-vite", "version": "0.5.0", "description": "chrome extension boilerplate", "license": "MIT", "private": true, "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/Jonghakseo/chrome-extension-boilerplate-react-vite.git"}, "type": "module", "scripts": {"clean:bundle": "rimraf dist && turbo clean:bundle", "clean:node_modules": "pnpx rimraf node_modules && pnpx turbo clean:node_modules", "clean:turbo": "rimraf .turbo && turbo clean:turbo", "clean": "pnpm clean:bundle && pnpm clean:turbo && pnpm clean:node_modules", "clean:install": "pnpm clean:node_modules && pnpm install --frozen-lockfile", "type-check": "turbo type-check", "base-build": "pnpm clean:bundle && turbo build", "build": "pnpm set-global-env && pnpm base-build", "build:firefox": "pnpm set-global-env CLI_CEB_FIREFOX=true && pnpm base-build", "base-dev": "pnpm clean:bundle && turbo ready && turbo watch dev", "dev": "pnpm set-global-env CLI_CEB_DEV=true && pnpm base-dev", "dev:firefox": "pnpm set-global-env CLI_CEB_DEV=true CLI_CEB_FIREFOX=true && pnpm base-dev", "zip": "pnpm build && pnpm -F zipper zip", "zip:firefox": "pnpm build:firefox && pnpm -F zipper zip", "e2e": "pnpm zip && turbo e2e", "e2e:firefox": "pnpm zip:firefox && turbo e2e", "lint": "turbo lint --continue", "lint:fix": "turbo lint:fix --continue", "format": "turbo format --continue -- --cache --cache-location node_modules/.cache/.prettiercache", "prepare": "husky", "update-version": "bash bash-scripts/update_version.sh", "copy-env": "bash bash-scripts/copy_env.sh", "set-global-env": "bash bash-scripts/set_global_env.sh", "module-manager": "pnpm -F module-manager start", "postinstall": "pnpm copy-env"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@types/chrome": "^0.0.323", "@types/eslint-plugin-jsx-a11y": "^6.10.0", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "deepmerge": "^4.3.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.3.5", "eslint-plugin-import-x": "^4.12.2", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-tailwindcss": "^3.18.0", "fast-glob": "^3.3.3", "fflate": "^0.8.2", "globals": "^16.1.0", "husky": "^9.1.7", "jiti": "^2.4.2", "lint-staged": "^16.0.0", "postcss": "^8.5.3", "postcss-load-config": "^6.0.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "rimraf": "^6.0.1", "run-script-os": "^1.1.6", "tailwindcss": "^3.4.17", "tslib": "^2.8.1", "tsx": "^4.19.4", "turbo": "^2.5.3", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vite-plugin-node-polyfills": "^0.23.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["prettier --write", "eslint --fix"]}, "packageManager": "pnpm@10.11.0", "engines": {"node": ">=22.15.1"}}