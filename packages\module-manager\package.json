{"name": "@extension/module-manager", "version": "0.5.0", "description": "chrome extension - module manager", "type": "module", "private": true, "sideEffects": true, "scripts": {"start": "tsx index.mts", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "format": "prettier . --write --ignore-path ../../.prettierignore"}, "devDependencies": {"@extension/dev-utils": "workspace:*", "@extension/shared": "workspace:*", "@extension/tsconfig": "workspace:*", "@inquirer/prompts": "^7.5.1", "@types/yargs": "^17.0.33", "tsx": "^4.19.4", "yargs": "^17.7.2"}}