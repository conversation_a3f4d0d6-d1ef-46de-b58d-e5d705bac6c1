{"name": "@extension/shared", "version": "0.5.0", "description": "chrome extension - shared code", "type": "module", "private": true, "sideEffects": false, "files": ["dist/**"], "types": "index.mts", "main": "dist/index.mjs", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "tsc -b", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "format": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit", "postinstall": "pnpm ready"}, "dependencies": {"react-error-boundary": "^6.0.0"}, "devDependencies": {"@extension/storage": "workspace:*", "@extension/tsconfig": "workspace:*", "type-fest": "^4.41.0"}}