/* COZE品牌主题样式 */
.popup-container {
  width: 320px;
  min-height: 380px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.coze-logo {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.title {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}

/* 内容区域 */
.content {
  padding: 20px;
}

.current-page-section {
  margin-bottom: 20px;
}

.current-page-label {
  color: #606266;
  font-size: 13px;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.current-page-url {
  font-size: 12px;
  line-height: 1.5;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  background: #f8f9fa;
  word-break: break-all;
}

.current-page-url.valid {
  border-color: #67c23a;
  background: #f0f9ff;
  color: #409eff;
}

.current-page-url.invalid {
  border-color: #f56c6c;
  background: #fef0f0;
  color: #f56c6c;
}

.current-page-url a {
  color: inherit;
  text-decoration: none;
}

.current-page-url a:hover {
  text-decoration: underline;
}

/* 主按钮样式 */
.fetch-btn {
  width: 100%;
  height: 44px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.fetch-btn.enabled {
  background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.fetch-btn.enabled:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  opacity: 0.95;
}

.fetch-btn.enabled:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
}

.fetch-btn.disabled {
  background: #e4e7ed;
  color: #a8abb2;
  cursor: not-allowed;
  box-shadow: none;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-message {
  margin-top: 16px;
  padding: 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
}

.error-message a {
  color: #409eff;
  text-decoration: none;
}

.error-message a:hover {
  text-decoration: underline;
}

/* 响应式调整 */
@media (max-width: 360px) {
  .popup-container {
    width: 300px;
  }

  .header {
    padding: 14px 16px;
  }

  .content {
    padding: 16px;
  }
}
