name: Make sure new PRs are sent to development

on:
  pull_request_target:
    types: [ opened ]

jobs:
  check-branch:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - uses: Vankka/pr-target-branch-action@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          target: main
          exclude: dev
          change-to: dev
          comment: |
            Your PR's base branch was set to `main`, PRs should be set to target `dev`.
            The base branch of this PR has been automatically changed to `dev`, please check that there are no merge conflicts.
          already-exists-action: close_this
          already-exists-comment: "Closing, because {url} basing on the same branch"
