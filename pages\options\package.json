{"name": "@extension/options", "version": "0.5.0", "description": "chrome extension - options", "type": "module", "private": true, "sideEffects": true, "files": ["dist/**"], "scripts": {"clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:turbo && pnpm clean:node_modules", "build": "vite build", "dev": "vite build --mode development", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "format": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "dependencies": {"@extension/shared": "workspace:*", "@extension/storage": "workspace:*", "@extension/ui": "workspace:*", "@extension/i18n": "workspace:*"}, "devDependencies": {"@extension/tailwindcss-config": "workspace:*", "@extension/tsconfig": "workspace:*", "@extension/vite-config": "workspace:*"}, "postcss": {"plugins": {"tailwindcss": {}, "autoprefixer": {}}}}